/**
 * Webhook Queue Utilities
 *
 * Provides utilities for managing webhook queue operations with duplicate prevention.
 * Implements atomic delete-then-insert operations to prevent race conditions and
 * ensure only the most recent webhook for each sourceId+type combination is processed.
 *
 * @fileoverview Webhook queue management utilities
 * @version 1.0.0
 * @since 2024-08-05
 */

import { dbSchema } from "@database";
import { and, eq, inArray, or } from "drizzle-orm";
import type { NeonHttpDatabase } from "drizzle-orm/neon-http";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { logInfo, logWarn, logError } from "@/utils/logger";

/**
 * Type for webhook queue insert operations
 */
type WebhookQueueInsert = typeof dbSchema.webhookQueue.$inferInsert;

/**
 * Type for webhook queue select operations
 */
type WebhookQueue = typeof dbSchema.webhookQueue.$inferSelect;

/**
 * Database type for the webhook queue operations
 */
type DrizzleDb = NeonHttpDatabase<typeof dbSchema>;

/**
 * Check for duplicate records before adding to queue
 *
 * Performs comprehensive duplicate prevention by checking:
 * 1. Local patient table for existing records matching the source ID
 * 2. Local appointment table for existing records matching the source ID
 * 3. Queue table for any existing records that match AP or CC IDs from found records
 * 4. Queue table for direct source ID matches
 *
 * @param db - Drizzle database instance
 * @param sourceId - Source ID from webhook payload
 * @param type - Webhook type (patient or appointment)
 * @returns Promise resolving to object with duplicate status and related IDs
 */
async function checkForDuplicates(
	db: DrizzleDb,
	sourceId: string,
	type: "patient" | "appointment",
): Promise<{ hasDuplicates: boolean; relatedIds: string[] }> {
	const relatedIds: string[] = [sourceId];

	try {
		if (type === "patient") {
			// Query patient table for existing records matching source ID
			const existingPatients = await db
				.select({
					apId: dbSchema.patient.apId,
					ccId: dbSchema.patient.ccId,
				})
				.from(dbSchema.patient)
				.where(
					or(
						eq(dbSchema.patient.apId, sourceId),
						eq(dbSchema.patient.ccId, parseInt(sourceId) || -1),
					),
				);

			// Extract AP and CC IDs from found patients
			for (const patient of existingPatients) {
				if (patient.apId) relatedIds.push(patient.apId);
				if (patient.ccId) relatedIds.push(patient.ccId.toString());
			}
		} else if (type === "appointment") {
			// Query appointment table for existing records matching source ID
			const existingAppointments = await db
				.select({
					apId: dbSchema.appointment.apId,
					ccId: dbSchema.appointment.ccId,
				})
				.from(dbSchema.appointment)
				.where(
					or(
						eq(dbSchema.appointment.apId, sourceId),
						eq(dbSchema.appointment.ccId, parseInt(sourceId) || -1),
					),
				);

			// Extract AP and CC IDs from found appointments
			for (const appointment of existingAppointments) {
				if (appointment.apId) relatedIds.push(appointment.apId);
				if (appointment.ccId) relatedIds.push(appointment.ccId.toString());
			}
		}

		// Check queue table for any existing records matching related IDs
		if (relatedIds.length > 1) {
			const existingQueueRecords = await db
				.select({ id: dbSchema.webhookQueue.id })
				.from(dbSchema.webhookQueue)
				.where(
					and(
						inArray(dbSchema.webhookQueue.sourceId, relatedIds),
						eq(dbSchema.webhookQueue.type, type),
					),
				)
				.limit(1);

			if (existingQueueRecords.length > 0) {
				logInfo(
					`Duplicate prevention: Found existing queue record for related IDs`,
					{
						sourceId,
						type,
						relatedIds,
						existingQueueRecordId: existingQueueRecords[0].id,
					},
				);
				return { hasDuplicates: true, relatedIds };
			}
		}

		return { hasDuplicates: false, relatedIds };
	} catch (error) {
		logWarn(
			`Error during duplicate check, allowing webhook to proceed: ${error}`,
			{
				sourceId,
				type,
				error: String(error),
			},
		);
		// Return false on error to allow processing (fail-safe)
		return { hasDuplicates: false, relatedIds };
	}
}

/**
 * Add webhook to queue with comprehensive duplicate prevention and status-based handling
 *
 * This function implements sophisticated duplicate prevention that handles existing webhooks
 * based on their current status:
 *
 * **Status-based handling:**
 * - **completed**: Move to logs with "completed" reason, continue processing new webhook
 * - **processing**: Ignore new webhook entirely (return null)
 * - **pending**: Update payload if exact match (same source & type), keep both if related
 * - **failed**: Check retry count, update for retry if eligible, otherwise move to logs
 *
 * **Matching logic:**
 * - **Exact match**: Same sourceId and type
 * - **Related match**: Different sourceId but same patient/appointment (via related IDs)
 *
 * @param db - Drizzle database instance
 * @param webhookData - Webhook data to insert
 * @returns Promise resolving to the created/updated webhook record, or null if ignored
 *
 * @throws {Error} When database operations fail
 *
 * @example
 * ```typescript
 * const result = await addWebhookToQueue(db, {
 *   source: "cc",
 *   sourceId: "123",
 *   type: "patient",
 *   payload: ccWebhookPayload,
 *   status: "pending"
 * });
 *
 * if (result) {
 *   console.log(`Webhook ${result.id} added/updated`);
 * } else {
 *   console.log("Webhook ignored due to active processing");
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function addWebhookToQueue(
	db: DrizzleDb,
	webhookData: WebhookQueueInsert,
): Promise<WebhookQueue | null> {
	// Step 1: Check for related records to understand the full context
	const duplicateCheck = await checkForDuplicates(
		db,
		webhookData.sourceId,
		webhookData.type as "patient" | "appointment",
	);

	// Step 2: Get all existing webhooks for exact matches (same sourceId + type)
	const exactMatches = await db
		.select()
		.from(dbSchema.webhookQueue)
		.where(
			and(
				eq(dbSchema.webhookQueue.sourceId, webhookData.sourceId),
				eq(dbSchema.webhookQueue.type, webhookData.type as "patient" | "appointment"),
			),
		);

	// Step 3: Get all existing webhooks for related matches (different sourceId but same patient/appointment)
	let relatedMatches: WebhookQueue[] = [];
	if (duplicateCheck.hasDuplicates && duplicateCheck.relatedIds.length > 1) {
		// Get related IDs excluding the current sourceId
		const otherRelatedIds = duplicateCheck.relatedIds.filter(id => id !== webhookData.sourceId);

		if (otherRelatedIds.length > 0) {
			relatedMatches = await db
				.select()
				.from(dbSchema.webhookQueue)
				.where(
					and(
						inArray(dbSchema.webhookQueue.sourceId, otherRelatedIds),
						eq(dbSchema.webhookQueue.type, webhookData.type as "patient" | "appointment"),
					),
				);
		}
	}

	// Step 4: Handle exact matches based on status
	for (const exactMatch of exactMatches) {
		switch (exactMatch.status) {
			case "completed":
				// Move completed record to logs
				try {
					await moveWebhookQueueRecordsToLogs(
						db,
						[exactMatch.id],
						"completed",
						{
							sourceId: webhookData.sourceId,
							type: webhookData.type,
							source: webhookData.source,
							reason: "Completed webhook superseded by new webhook",
						},
					);
					logInfo(
						`Moved completed webhook to logs: ${exactMatch.id} for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
					);
				} catch (error) {
					logWarn(
						`Failed to move completed webhook to logs: ${error}`,
						{ webhookId: exactMatch.id, error: String(error) },
					);
				}
				break;

			case "processing":
				// Ignore new webhook entirely
				logInfo(
					`Duplicate prevention: Ignoring new webhook for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type} - webhook currently processing`,
					{ processingWebhookId: exactMatch.id },
				);
				return null;

			case "pending":
				// Update existing webhook payload with new data
				try {
					const updatedWebhook = await db
						.update(dbSchema.webhookQueue)
						.set({
							payload: webhookData.payload,
							updatedAt: new Date(),
						})
						.where(eq(dbSchema.webhookQueue.id, exactMatch.id))
						.returning();

					logInfo(
						`Updated pending webhook payload: ${exactMatch.id} for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
					);
					return updatedWebhook[0];
				} catch (error) {
					logError(
						`Failed to update pending webhook payload: ${error}`,
						{ webhookId: exactMatch.id, error: String(error) },
					);
					throw new Error(`Failed to update pending webhook: ${error}`);
				}

			case "failed":
				// Check retry count and handle accordingly
				if (exactMatch.retryCount >= (exactMatch.maxRetries || 3)) {
					// Move to logs - max retries exceeded
					try {
						await moveWebhookQueueRecordsToLogs(
							db,
							[exactMatch.id],
							"failed",
							{
								sourceId: webhookData.sourceId,
								type: webhookData.type,
								source: webhookData.source,
								reason: "Max retries exceeded, superseded by new webhook",
								retryCount: exactMatch.retryCount,
								maxRetries: exactMatch.maxRetries,
							},
						);
						logInfo(
							`Moved failed webhook to logs (max retries exceeded): ${exactMatch.id} for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
						);
					} catch (error) {
						logWarn(
							`Failed to move failed webhook to logs: ${error}`,
							{ webhookId: exactMatch.id, error: String(error) },
						);
					}
				} else {
					// Update payload and reset for retry
					try {
						const updatedWebhook = await db
							.update(dbSchema.webhookQueue)
							.set({
								payload: webhookData.payload,
								status: "pending",
								errorMessage: null,
								errorDetails: null,
								updatedAt: new Date(),
							})
							.where(eq(dbSchema.webhookQueue.id, exactMatch.id))
							.returning();

						logInfo(
							`Reset failed webhook for retry: ${exactMatch.id} for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
						);
						return updatedWebhook[0];
					} catch (error) {
						logError(
							`Failed to reset failed webhook for retry: ${error}`,
							{ webhookId: exactMatch.id, error: String(error) },
						);
						throw new Error(`Failed to reset failed webhook: ${error}`);
					}
				}
				break;
		}
	}

	// Step 5: Handle related matches (different sourceId but same patient/appointment)
	for (const relatedMatch of relatedMatches) {
		switch (relatedMatch.status) {
			case "completed":
				// Move completed related records to logs
				try {
					await moveWebhookQueueRecordsToLogs(
						db,
						[relatedMatch.id],
						"completed",
						{
							sourceId: webhookData.sourceId,
							type: webhookData.type,
							source: webhookData.source,
							reason: "Completed related webhook superseded by new webhook",
							relatedSourceId: relatedMatch.sourceId,
						},
					);
					logInfo(
						`Moved completed related webhook to logs: ${relatedMatch.id} (${relatedMatch.source}:${relatedMatch.sourceId}) for new ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
					);
				} catch (error) {
					logWarn(
						`Failed to move completed related webhook to logs: ${error}`,
						{ webhookId: relatedMatch.id, error: String(error) },
					);
				}
				break;

			case "processing":
				// Log but don't interfere with processing related webhooks
				logInfo(
					`Related webhook currently processing: ${relatedMatch.id} (${relatedMatch.source}:${relatedMatch.sourceId}) while adding new ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
				);
				break;

			case "pending":
			case "failed":
				// Keep both - different source but same patient/appointment
				logInfo(
					`Keeping related webhook: ${relatedMatch.id} (${relatedMatch.source}:${relatedMatch.sourceId}) alongside new ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
				);
				break;
		}
	}

	// Step 6: Insert the new webhook record
	const [createdWebhook] = await db
		.insert(dbSchema.webhookQueue)
		.values(webhookData)
		.returning();

	if (!createdWebhook) {
		throw new Error("Failed to create webhook record");
	}

	logInfo(
		`Webhook added to queue: ${createdWebhook.id} for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
		{
			webhookId: createdWebhook.id,
			sourceId: webhookData.sourceId,
			type: webhookData.type,
			source: webhookData.source,
			exactMatches: exactMatches.length,
			relatedMatches: relatedMatches.length,
		},
	);

	return createdWebhook;
}

/**
 * Extract source information from webhook payload
 *
 * Determines the source platform, sourceId, and type from webhook payload.
 * Used to normalize webhook data before queue insertion.
 *
 * @param payload - AP or CC webhook payload
 * @returns Object containing source, sourceId, and type information
 *
 * @throws {Error} When payload is invalid or missing required fields
 *
 * @example
 * ```typescript
 * const sourceInfo = extractSourceInfo(ccWebhookPayload);
 * // Returns: { source: "cc", sourceId: "123", type: "patient" }
 * ```
 *
 * @since 1.0.0
 */
export function extractSourceInfo(
	payload: APContactCreationWebhookPayload | CCWebhookPayload,
): {
	source: "ap" | "cc";
	sourceId: string;
	type: "patient" | "appointment";
} {
	// Check if it's an AP webhook payload
	if ("contact_id" in payload) {
		return {
			source: "ap",
			sourceId: payload.contact_id,
			type: payload.calendar ? "appointment" : "patient",
		};
	}

	// Check if it's a CC webhook payload
	if ("id" in payload) {
		return {
			source: "cc",
			sourceId: payload.id.toString(),
			type:
				payload.model?.toLowerCase() === "appointment"
					? "appointment"
					: "patient",
		};
	}

	throw new Error(
		"Invalid webhook payload: missing required identification fields",
	);
}

/**
 * Move webhook queue records to logs table instead of permanently deleting them
 *
 * This function preserves audit trail by copying webhook queue records to the
 * queueLogs table before deletion. This allows for historical tracking and
 * debugging while maintaining queue performance.
 *
 * @param db - Drizzle database instance
 * @param webhookIds - Array of webhook queue IDs to move to logs
 * @param deletionReason - Reason for deletion (completed, duplicate_prevention, failed, stuck)
 * @param deletionContext - Additional context about the deletion
 * @returns Promise resolving to the number of records moved
 *
 * @throws {Error} When database operations fail
 *
 * @example
 * ```typescript
 * await moveWebhookQueueRecordsToLogs(db, ["webhook-id-1"], "completed", {
 *   processingDuration: 5000,
 *   success: true
 * });
 * ```
 *
 * @since 1.0.0
 */
export async function moveWebhookQueueRecordsToLogs(
	db: DrizzleDb,
	webhookIds: string[],
	deletionReason: "completed" | "duplicate_prevention" | "failed" | "stuck",
	deletionContext?: Record<string, unknown>,
): Promise<number> {
	if (webhookIds.length === 0) {
		return 0;
	}

	try {
		// Step 1: Fetch the webhook records to be moved
		const webhookRecords = await db
			.select()
			.from(dbSchema.webhookQueue)
			.where(inArray(dbSchema.webhookQueue.id, webhookIds));

		if (webhookRecords.length === 0) {
			logWarn("No webhook records found to move to logs", {
				webhookIds,
				deletionReason,
			});
			return 0;
		}

		// Step 2: Insert records into queue logs table
		const logRecords = webhookRecords.map((record) => ({
			originalId: record.id,
			source: record.source,
			sourceId: record.sourceId,
			type: record.type,
			payload: record.payload,
			status: record.status,
			retryCount: record.retryCount,
			lastRetryAttemptAt: record.lastRetryAttemptAt,
			lastRetryReason: record.lastRetryReason,
			maxRetries: record.maxRetries,
			processingStartedAt: record.processingStartedAt,
			processingCompletedAt: record.processingCompletedAt,
			errorMessage: record.errorMessage,
			errorDetails: record.errorDetails,
			originalCreatedAt: record.createdAt,
			originalUpdatedAt: record.updatedAt,
			deletionReason,
			deletionContext,
		}));

		await db.insert(dbSchema.queueLogs).values(logRecords);

		// Step 3: Delete records from webhook queue
		await db
			.delete(dbSchema.webhookQueue)
			.where(inArray(dbSchema.webhookQueue.id, webhookIds));

		logInfo(
			`Moved ${webhookRecords.length} webhook queue record(s) to logs`,
			{
				webhookIds,
				deletionReason,
				movedCount: webhookRecords.length,
			},
		);

		return webhookRecords.length;
	} catch (error) {
		logWarn(
			`Failed to move webhook queue records to logs: ${error}`,
			{
				webhookIds,
				deletionReason,
				error: String(error),
			},
		);
		throw new Error(`Failed to move webhook queue records to logs: ${error}`);
	}
}
